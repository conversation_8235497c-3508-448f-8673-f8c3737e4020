
import type { NextAuthConfig } from "next-auth";
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials";
import { prisma } from "@/lib/db";
import bcrypt from "bcryptjs";
import { env } from "@/env.mjs";

import Google from "next-auth/providers/google";


export default {
  providers: [

    Google({
      clientId: env.GOOGLE_CLIENT_ID,
      clientSecret: env.GOOGLE_CLIENT_SECRET,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) return null;
        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
          select: {
            id: true,
            name: true,
            email: true,
            password: true,
            emailVerified: true,
            role: true,
            image: true,
          },
        });
        if (!user || typeof user.password !== "string") return null;
        if (!user.emailVerified) {
          throw new Error("Please verify your email. OTP has been sent.");
        }
        const isValid = await bcrypt.compare(credentials.password, user.password);
        if (!isValid) return null;
        return {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          image: user.image
        };
      }
    })
  ],
} satisfies NextAuthConfig;
