"use client";

import * as React from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { signIn } from "next-auth/react";
import { useForm } from "react-hook-form";
import * as z from "zod";

import { cn } from "@/lib/utils";
import { userAuthSchema } from "@/lib/validations/auth";
import { buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Icons } from "@/components/shared/icons";
import { OTPVerifyForm } from "@/components/forms/otp-verify-form";

interface UserAuthFormProps extends React.HTMLAttributes<HTMLDivElement> {
  type?: string;
}

type FormData = z.infer<typeof userAuthSchema>;

export function UserAuthForm({ className, type, ...props }: User<PERSON>uthFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(userAuthSchema),
  });
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [isGoogleLoading, setIsGoogleLoading] = React.useState<boolean>(false);
  const [showOTP, setShowOTP] = React.useState(false);
  const [registeredEmail, setRegisteredEmail] = React.useState<string>("");
  const [loginPassword, setLoginPassword] = React.useState<string>("");
  const searchParams = useSearchParams();
  const router = useRouter();

  async function onSubmit(data: FormData) {
    setIsLoading(true);

    if (type === "register") {
      // Registration logic: call your registration API
      const res = await fetch("/api/auth/register", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: data.name,
          email: data.email,
          password: data.password,
          mobileNumber: data.mobileNumber,
          gender: data.gender,
          dateOfBirth: data.dateOfBirth,
        })
      });
      setIsLoading(false);
      if (!res.ok) {
        const result = await res.json().catch(() => ({}));
        return toast.error(result?.message || "Registration failed");
      }
      setRegisteredEmail(data.email);
      setLoginPassword(data.password);
      setShowOTP(true);
      toast.success("Registration successful! Please verify OTP sent to your email.");
      return;
    }

    // Login logic: use Credentials provider
    const signInResult = await signIn("credentials", {
      email: data.email.toLowerCase(),
      password: data.password,
      redirect: false,
      callbackUrl: searchParams?.get("from") || "/dashboard",
    });

    setIsLoading(false);

    if (!signInResult?.ok) {
      // If error is about email verification, show OTP form
      if (signInResult?.error?.toLowerCase().includes("verify your email")) {
        setRegisteredEmail(data.email);
        setLoginPassword(data.password);
        setShowOTP(true);
        toast.info("Please verify your email. OTP has been sent.");
        return;
      }
      return toast.error("Invalid email or password.");
    }
    toast.success("Signed in successfully.");
    // Redirect to dashboard or callbackUrl
    router.push(signInResult?.url || "/dashboard");
  }

  if (showOTP && registeredEmail) {
    return (
      <div className={cn("grid gap-6", className)} {...props}>
        <OTPVerifyForm
          email={registeredEmail}
          password={loginPassword}
          autoSignIn={true}
          onSuccess={() => {
            setShowOTP(false);
            toast.success("OTP verified! You are now signed in.");
          }}
        />
      </div>
    );
  }

  return (
    <div className={cn("grid gap-6", className)} {...props}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid gap-2">
          {type === "register" && (
            <>
              <div className="grid gap-1">
                <Label className="sr-only" htmlFor="name">
                  Name
                </Label>
                <Input
                  id="name"
                  placeholder="Your name"
                  type="text"
                  autoComplete="name"
                  disabled={isLoading || isGoogleLoading}
                  {...register("name")}
                />
                {errors?.name && (
                  <p className="px-1 text-xs text-red-600">{errors.name.message}</p>
                )}
              </div>
              <div className="grid gap-1">
                <Label className="sr-only" htmlFor="mobileNumber">
                  Mobile Number
                </Label>
                <Input
                  id="mobileNumber"
                  placeholder="Mobile number"
                  type="tel"
                  autoComplete="tel"
                  disabled={isLoading || isGoogleLoading}
                  {...register("mobileNumber")}
                />
                {errors?.mobileNumber && (
                  <p className="px-1 text-xs text-red-600">{errors.mobileNumber.message}</p>
                )}
              </div>
              <div className="grid gap-1">
                <Label className="sr-only" htmlFor="gender">
                  Gender
                </Label>
                <select
                  id="gender"
                  className="input"
                  disabled={isLoading || isGoogleLoading}
                  {...register("gender")}
                >
                  <option value="">Select gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
                {errors?.gender && (
                  <p className="px-1 text-xs text-red-600">{errors.gender.message}</p>
                )}
              </div>
              <div className="grid gap-1">
                <Label className="sr-only" htmlFor="dateOfBirth">
                  Date of Birth
                </Label>
                <Input
                  id="dateOfBirth"
                  placeholder="Date of birth"
                  type="date"
                  autoComplete="bday"
                  disabled={isLoading || isGoogleLoading}
                  {...register("dateOfBirth")}
                />
                {errors?.dateOfBirth && (
                  <p className="px-1 text-xs text-red-600">{errors.dateOfBirth.message}</p>
                )}
              </div>
            </>
          )}
          <div className="grid gap-1">
            <Label className="sr-only" htmlFor="email">
              Email
            </Label>
            <Input
              id="email"
              placeholder="<EMAIL>"
              type="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isLoading || isGoogleLoading}
              {...register("email")}
            />
            {errors?.email && (
              <p className="px-1 text-xs text-red-600">
                {errors.email.message}
              </p>
            )}
          </div>
          <div className="grid gap-1">
            <Label className="sr-only" htmlFor="password">
              Password
            </Label>
            <Input
              id="password"
              placeholder="Password"
              type="password"
              autoComplete={type === "register" ? "new-password" : "current-password"}
              disabled={isLoading || isGoogleLoading}
              {...register("password")}
            />
            {errors?.password && (
              <p className="px-1 text-xs text-red-600">
                {errors.password.message}
              </p>
            )}
          </div>
          <button className={cn(buttonVariants())} disabled={isLoading}>
            {isLoading && (
              <Icons.spinner className="mr-2 size-4 animate-spin" />
            )}
            {type === "register" ? "Sign Up" : "Sign In"}
          </button>
        </div>
      </form>
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or continue with
          </span>
        </div>
      </div>
      <button
        type="button"
        className={cn(buttonVariants({ variant: "outline" }))}
        onClick={() => {
          setIsGoogleLoading(true);
          signIn("google");
        }}
        disabled={isLoading || isGoogleLoading}
      >
        {isGoogleLoading ? (
          <Icons.spinner className="mr-2 size-4 animate-spin" />
        ) : (
          <Icons.google className="mr-2 size-4" />
        )}{" "}
        Google
      </button>
    </div>
  );
}
