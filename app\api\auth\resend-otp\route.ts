import { NextResponse } from "next/server";
import { prisma } from "@/lib/db";
import { resend } from "@/lib/email";

export async function POST(req: Request) {
  try {
    const { email } = await req.json();
    if (!email) {
      return NextResponse.json({ message: "Email is required." }, { status: 400 });
    }
    const user = await prisma.user.findUnique({ where: { email } });
    if (!user) {
      return NextResponse.json({ message: "User not found." }, { status: 404 });
    }
    // Generate new OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now
    await prisma.user.update({
      where: { email },
      data: { otp, otpExpires },
    });
    // Send OTP email
    await resend.emails.send({
      from: 'Next Template App <<EMAIL>>',
      to: email,
      subject: 'Your OTP Code',
      text: `Your new OTP code is: ${otp}`
    });
    return NextResponse.json({ message: "OTP resent successfully." }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ message: "Internal server error." }, { status: 500 });
  }
}
